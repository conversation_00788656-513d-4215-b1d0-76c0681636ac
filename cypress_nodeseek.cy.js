describe('Auto Login MT', () => {
    const filters = {
        'yxvm': ['vol', '3'],
        'claw': ['出']
    };

    beforeEach(() => {
        // 清除cookies和localStorage，确保每次测试都是全新状态
        cy.clearCookies();
        cy.clearLocalStorage();
        
        // 设置更真实的浏览器环境
        cy.window().then((win) => {
            // 移除webdriver标识
            delete win.navigator.__proto__.webdriver;
        });

    // 主要逻辑函数
    function performMainLogic($body) {
        // 检查是否需要登录
        if ($body.find('.login-btn, [href*="login"], .user-login').length > 0) {
            performLogin();
        }

        // 获取所有的post-list-item并进行过滤检查
        cy.get('.post-list-item', { timeout: 10000 }).then(($items) => {
            if ($items.length === 0) {
                cy.log('❌ 未找到帖子列表，可能页面结构已改变或被阻止访问');
                return;
            }
            
            cy.log(`✅ 找到 ${$items.length} 个帖子项目`);
            
            // 遍历每个帖子项目
            $items.each((index, item) => {
                const $item = Cypress.$(item);
                const titleText = $item.find('.post-title').text().toLowerCase();
                const contentText = $item.text().toLowerCase();
                
                cy.log(`检查第 ${index + 1} 个帖子: ${titleText}`);
                
                // 检查过滤条件
                checkFilters(titleText, contentText, index + 1);
            });
        }).catch((error) => {
            cy.log('❌ 获取帖子列表失败，可能是页面元素选择器不正确');
            cy.log(`错误信息: ${error.message}`);
        });
    });

    it('MT自动登录程序', () => {
        // 使用自定义命令访问网站，增加重试机制
        cy.visitWithRetry('/');
        
        // 人性化延迟
        cy.humanDelay(1000, 3000);
        
        // 检查页面是否正常加载
        cy.url().should('include', 'nodeseek.com');
        
        // 尝试获取页面内容，如果失败则记录错误
        cy.get('body').then(($body) => {
            const bodyText = $body.text();
            if (bodyText.includes('403') || bodyText.includes('Forbidden') || bodyText.includes('Access Denied')) {
                cy.log('⚠️ 检测到403错误，尝试以下解决方案：');
                cy.log('1. 检查IP是否被封禁');
                cy.log('2. 尝试使用代理');
                cy.log('3. 降低访问频率');
                cy.log('4. 手动打开浏览器访问一次网站');
                return;
            }
            
            cy.log('✅ 页面加载成功，开始执行主要逻辑');
            performMainLogic($body);
        });
    });

    // 执行登录的函数
    function performLogin() {
        // 这里需要根据实际的登录流程来调整
        // 示例登录流程：
        cy.get('.login-btn, [href*="login"], .user-login').first().click();
        
        // 等待登录页面加载
        cy.wait(1000);
        
        // 输入用户名和密码（需要根据实际页面元素调整）
        cy.get('input[name="username"], input[type="email"], #username, #email')
            .type(Cypress.env('username') || 'your_username');
        
        cy.get('input[name="password"], input[type="password"], #password')
            .type(Cypress.env('password') || 'your_password');
        
        // 点击登录按钮
        cy.get('button[type="submit"], .login-submit, .btn-login').click();
        
        // 等待登录完成
        cy.wait(3000);
    }

    // 检查过滤条件的函数
    function checkFilters(titleText, contentText, itemIndex) {
        const fullText = titleText + ' ' + contentText;
        
        Object.keys(filters).forEach(keyword => {
            if (fullText.includes(keyword)) {
                const requiredTerms = filters[keyword];
                const hasRequiredTerms = requiredTerms.some(term => 
                    fullText.includes(term.toLowerCase())
                );
                
                if (hasRequiredTerms) {
                    cy.log(`✅ 第 ${itemIndex} 个帖子匹配过滤条件:`);
                    cy.log(`   关键词: ${keyword}`);
                    cy.log(`   包含条件: ${requiredTerms.join(' 或 ')}`);
                    cy.log(`   标题: ${titleText}`);
                    
                    // 可以在这里添加更多操作，比如点击帖子、收藏等
                    // cy.get(`.post-list-item:nth-child(${itemIndex})`).click();
                } else {
                    cy.log(`❌ 第 ${itemIndex} 个帖子包含 "${keyword}" 但不满足条件`);
                }
            }
        });
    }

    // 额外的测试用例：验证特定帖子
    it('验证特定帖子内容', () => {
        cy.visit('https://www.nodeseek.com/');
        cy.wait(2000);

        // 检查第2个帖子的标题
        cy.get('.post-list-item:nth-child(2) .post-title')
            .should('be.visible')
            .then(($title) => {
                const titleText = $title.text();
                cy.log(`第2个帖子标题: ${titleText}`);
                
                // 可以添加断言来验证标题内容
                // expect(titleText).to.include('预期的关键词');
            });
    });

    // 辅助方法：等待元素加载
    it('等待页面元素加载完成', () => {
        cy.visit('https://www.nodeseek.com/');
        
        // 等待关键元素加载
        cy.get('.post-list-item', { timeout: 10000 }).should('have.length.greaterThan', 0);
        cy.get('.post-title', { timeout: 10000 }).should('be.visible');
        
        cy.log('页面元素加载完成');
    });
});