{"name": "nodeseek-watcher", "version": "1.0.0", "description": "NodeSeek监控程序 - 自动访问和监控NodeSeek网站内容", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node src/index.js --dev", "test": "node src/test.js"}, "keywords": ["nodeseek", "monitor", "watcher", "automation"], "author": "", "license": "MIT", "dependencies": {"puppeteer": "^21.5.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-user-preferences": "^2.4.1", "chalk": "^4.1.2", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2"}}