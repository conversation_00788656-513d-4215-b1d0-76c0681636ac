// 配置文件
module.exports = {
  // 网站配置
  website: {
    baseUrl: 'https://www.nodeseek.com',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 2000
  },

  // 浏览器配置
  browser: {
    headless: false, // 设为false可以看到浏览器操作过程
    viewport: {
      width: 1920,
      height: 1080
    },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-blink-features=AutomationControlled',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-ipc-flooding-protection'
    ]
  },

  // 监控配置
  monitor: {
    // 监控间隔（毫秒）
    interval: 60000, // 1分钟
    
    // 过滤条件
    filters: {
      'yxvm': ['vol', '3'],
      'claw': ['出']
    },
    
    // 是否显示所有标题
    showAllTitles: true,
    
    // 最大显示标题数量
    maxTitles: 20
  },

  // 日志配置
  logging: {
    level: 'info', // debug, info, warn, error
    showTimestamp: true,
    colorOutput: true
  }
};
