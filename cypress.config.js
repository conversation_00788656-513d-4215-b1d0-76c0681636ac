// cypress.config.js
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    // 基础配置
    baseUrl: 'https://www.nodeseek.com',
    viewportWidth: 1920,
    viewportHeight: 1080,
    
    // 超时配置
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // 重试配置
    retries: {
      runMode: 2,
      openMode: 1,
    },
    
    // 视频和截图
    video: true,
    screenshotOnRunFailure: true,
    
    // 环境变量
    env: {
      username: 'your_username', // 替换为实际用户名
      password: 'your_password', // 替换为实际密码
    },
    
    setupNodeEvents(on, config) {
      // 在这里可以添加插件和任务
      
      // 添加任务来处理一些Node.js操作
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
      });
      
      // 修改浏览器启动参数
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          // 添加反检测参数
          launchOptions.args.push('--disable-blink-features=AutomationControlled');
          launchOptions.args.push('--disable-web-security');
          launchOptions.args.push('--disable-features=VizDisplayCompositor');
          launchOptions.args.push('--disable-ipc-flooding-protection');
          launchOptions.args.push('--no-sandbox');
          launchOptions.args.push('--disable-dev-shm-usage');
          
          // 设置用户代理
          launchOptions.args.push('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
          
          return launchOptions;
        }
        
        if (browser.family === 'firefox') {
          // Firefox配置
          launchOptions.preferences['general.useragent.override'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
          return launchOptions;
        }
      });
      
      return config;
    },
  },
});

// cypress/support/e2e.js - 全局配置
// 添加以下内容到你的 cypress/support/e2e.js 文件中

// 禁用Chrome的自动化检测
Cypress.on('window:before:load', (win) => {
  // 移除webdriver属性
  delete win.navigator.__proto__.webdriver;
  
  // 重写chrome属性
  win.navigator.chrome = {
    runtime: {},
  };
  
  // 重写permissions属性
  const originalQuery = win.navigator.permissions.query;
  win.navigator.permissions.query = (parameters) => (
    parameters.name === 'notifications' ?
      Promise.resolve({ state: Cypress._.sample(['granted', 'default', 'denied']) }) :
      originalQuery(parameters)
  );
  
  // 重写plugins属性
  Object.defineProperty(win.navigator, 'plugins', {
    get: () => [1, 2, 3, 4, 5],
  });
  
  // 重写languages属性
  Object.defineProperty(win.navigator, 'languages', {
    get: () => ['zh-CN', 'zh', 'en'],
  });
});

// 添加自定义命令
Cypress.Commands.add('visitWithRetry', (url, options = {}) => {
  const defaultOptions = {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Upgrade-Insecure-Requests': '1',
    },
    failOnStatusCode: false,
    timeout: 30000,
    ...options
  };
  
  return cy.visit(url, defaultOptions).then(() => {
    // 等待页面加载完成
    cy.wait(2000);
    
    // 检查是否被阻止
    cy.get('body').then(($body) => {
      const bodyText = $body.text();
      if (bodyText.includes('403') || bodyText.includes('Forbidden') || bodyText.includes('Access Denied')) {
        cy.log('⚠️ 检测到访问被阻止，尝试刷新页面');
        cy.reload();
        cy.wait(3000);
      }
    });
  });
});

// 添加延迟命令来模拟人类行为
Cypress.Commands.add('humanDelay', (min = 500, max = 2000) => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  cy.wait(delay);
});

// 添加更智能的点击命令
Cypress.Commands.add('humanClick', { prevSubject: 'element' }, (subject, options = {}) => {
  cy.wrap(subject)
    .scrollIntoView()
    .wait(Math.floor(Math.random() * 1000) + 500)
    .click(options);
});